import type { z } from 'zod'
import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import { consola } from 'consola'
import { getLogoConfigById } from '../../shared/utils/config-utils.ts'
import LogoLookupService from '../../shared/services/logo-lookup-service.ts'
import { convertTimeToLogoInt } from '../../shared/utils/date-utils.ts'

/**
 * Service for handling Logo REST API operations for Satis Faturalari
 */
const SatisFaturalariLogoRestService = {
  /**
   * Gets an access token from the Logo REST API.
   * If logoCredentials is provided, it will use those credentials instead of the ones in the config.
   */
  getToken: async ({
    veritabaniId,
    logoCredentials,
  }: {
    veritabaniId: string
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<string> => {
    const logoConfig = await getLogoConfigById(veritabaniId)
    const restApiUrl = logoConfig.erp.rest_settings.rest_api_url
    const username = logoCredentials?.kullanici_adi || logoConfig.erp.kullanici_adi
    const password = logoCredentials?.sifre || logoConfig.erp.sifre
    const firmno = logoConfig.erp.firma_numarasi
    const clientKey = logoConfig.erp.rest_settings.client_key

    if (!restApiUrl || !username || !password || !firmno || !clientKey) {
      throw new Error(`Logo REST API yapılandırması eksik, veritabanı ID: ${veritabaniId}`)
    }

    try {
      consola.info(`Logo erişim anahtarı isteniyor: ${restApiUrl}/token`)
      const response = await fetch(`${restApiUrl}/token`, {
        method: 'POST',
        body: `grant_type=password&username=${username}&firmno=${firmno}&password=${password}`,
        headers: { 'Content-Type': 'application/x-www-form-urlencoded', 'Authorization': `Basic ${clientKey}` },
      })
      const data = (await response.json()) as { access_token?: string, error?: string, error_description?: string }

      if (!response.ok || !data.access_token) {
        const errorMsg = `Logo Rest Servisi Hatası - getToken: ${data.error || 'Erişim anahtarı alınamadı'}. ${data.error_description || ''} Durum: ${response.status} ${response.statusText}`
        consola.error(errorMsg, { responseData: data })
        throw new Error(errorMsg)
      }

      consola.success('Logo erişim anahtarı başarıyla alındı.')
      return data.access_token
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - getToken:', error)
      throw error // Re-throw after logging
    }
  },

  /**
   * Revokes an access token from the Logo REST API.
   */
  revokeToken: async ({ accessToken, veritabaniId }: { accessToken: string, veritabaniId: string }): Promise<void> => {
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const restApiUrl = logoConfig.erp.rest_settings.rest_api_url

      if (!restApiUrl) {
        throw new Error(`Veritabanı ID için Logo REST API URL'si bulunamadı: ${veritabaniId}`)
      }

      consola.info(`Logo erişim anahtarı iptal ediliyor: ${restApiUrl}/revoke`)
      const response = await fetch(`${restApiUrl}/revoke`, { method: 'GET', headers: { Authorization: `Bearer ${accessToken}` } })

      if (!response.ok) {
        const data = await response.text()
        const errorMsg = `Logo Rest Servisi Hatası - revokeToken: Durum: ${response.status} ${response.statusText}. Yanıt: ${data}`
        consola.error(errorMsg)
        // Don't throw here, just log the error as revoke failure might not be critical
      }
      else {
        consola.success('Logo erişim anahtarı başarıyla iptal edildi.')
      }
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - revokeToken:', error)
      // Don't re-throw revoke errors
    }
  },

  /**
   * Transforms Mizan request data to REST API format for Logo REST API integration
   * Used when use_rest=true
   */
  transformToRestFormat: async (
    requestData: z.infer<typeof satisFaturalariSchema>,
    veritabaniId: string,
  ): Promise<SatisFaturaInput> => {
    // Get currency information - use default TL since para_birimi is not in main schema
    const currencyInfo = await LogoLookupService.getCurrencyTypeFromCode('TL', veritabaniId)

    // Get salesman information if provided
    let salesmanRef: number | undefined
    if (requestData.satis_elemani) {
      salesmanRef = await LogoLookupService.getSalesmanRefFromCode(requestData.satis_elemani, veritabaniId)
    }

    // Transform header data for REST API format
    const invoice = {
      TYPE: requestData.fatura_turu,
      NUMBER: requestData.fatura_no || '~',
      DATE: requestData.tarihi,
      TIME: convertTimeToLogoInt(requestData.saati),
      DOC_TRACK_NR: requestData.belge_no || '', // Use belge_no instead of belge_takip_no
      ARP_CODE: requestData.cari_kodu || '',
      GL_CODE: '', // muhasebe_kodu not in schema
      CURR_INVOICE: currencyInfo || 0,
      TC_XRATE: requestData.doviz_kuru || 1,
      RC_XRATE: 1, // raporlama_doviz_kuru not in schema
      NOTES1: requestData.aciklama || '',
      NOTES2: '', // aciklama2 not in schema
      NOTES3: '', // aciklama3 not in schema
      NOTES4: '', // aciklama4 not in schema
      GENEXP1: requestData.ozel_kod || '',
      GENEXP2: '', // ozel_kod2 not in schema
      GENEXP3: '', // ozel_kod3 not in schema
      GENEXP4: '', // ozel_kod4 not in schema
      GENEXP5: '', // ozel_kod5 not in schema
      GENEXP6: '', // ozel_kod6 not in schema
      PROJECT_CODE: requestData.proje_kodu || '',
      PAYMENT_CODE: '', // odeme_plani not in schema
      SALESMAN_CODE: requestData.satis_elemani || '',
      SALESMANREF: salesmanRef || 0,
      SOURCEINDEX: requestData.ambar_kodu || 0,
      FACTORY: requestData.fabrika_kodu || 0,
      DIVISION: requestData.isyeri_kodu || 0,
      DEPARTMENT: requestData.bolum_kodu || 0,
      AUXIL_CODE: '', // hareket_ozel_kodu not in main schema
      SHIP_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of sevk_tarihi
      SHIP_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of sevk_saati
      DELIVERY_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of teslim_tarihi
      DELIVERY_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of teslim_saati
      EINVOICE: 0, // e_fatura not in schema
      EINVOICE_TYPE: 0, // e_fatura_tipi not in schema
      PROFILE_ID: 0, // profil_id not in schema
      AFFECT_RISK: 1,
      // REST API specific fields
      INVOICE_SERIES: '', // fatura_seri not in schema
      INVOICE_NUMBER_FORMAT: requestData.fatura_numarasi_formati || '',
      DISPATCH_SERIES: '', // irsaliye_seri not in schema
      DISPATCH_NUMBER_FORMAT: requestData.irsaliye_numarasi_formati || '',
    }

    // Transform line items for REST API format
    const transactions = {
      items: await Promise.all(
        (requestData.fatura_satirlari || []).map(async (satir, index) => {
          // Get currency info for line item
          const lineCurrencyInfo = await LogoLookupService.getCurrencyTypeFromCode(satir.para_birimi || 'TL', veritabaniId)

          // Get salesman info for line item if provided
          let lineSalesmanRef: number | undefined
          if (satir.satis_elemani) {
            lineSalesmanRef = await LogoLookupService.getSalesmanRefFromCode(satir.satis_elemani, veritabaniId)
          }

          return {
            TYPE: satir.satir_turu || 0,
            MASTER_CODE: satir.malzeme_kodu || '',
            QUANTITY: satir.miktar || 0,
            PRICE: satir.birim_fiyat || 0,
            TOTAL: (satir.miktar || 0) * (satir.birim_fiyat || 0),
            CURR_TRANSACTION: lineCurrencyInfo || 0,
            TC_XRATE: satir.doviz_kuru || 1,
            RC_XRATE: 1, // raporlama_doviz_kuru not in line schema
            DESCRIPTION: satir.aciklama || '',
            UNIT_CODE: satir.birim_kodu || 'ADET',
            UNIT_CONV1: 1,
            UNIT_CONV2: 1,
            VAT_RATE: satir.kdv_orani || 0,
            VAT_INCLUDED: 0, // kdv_dahil not in line schema
            DISCOUNT_RATE: satir.indirim_orani || 0,
            SOURCEINDEX: satir.ambar_kodu || requestData.ambar_kodu || 0,
            FACTORY: satir.fabrika_kodu || requestData.fabrika_kodu || 0,
            PROJECT_CODE: satir.proje_kodu || requestData.proje_kodu || '',
            AUXIL_CODE: satir.hareket_ozel_kodu || '',
            SALEMANCODE: satir.satis_elemani || requestData.satis_elemani || '',
            SALESMANREF: lineSalesmanRef || salesmanRef || 0,
            EDT_PRICE: satir.dovizli_birim_fiyat || satir.birim_fiyat || 0,
            LINE_NR: index + 1,
            // REST API specific fields
            DISCOUNT_AMOUNT: satir.indirim_tutari || 0,
            NET_PRICE: (satir.birim_fiyat || 0) - (satir.indirim_tutari || 0),
          }
        }),
      ),
    }

    // Transform dispatch data for REST API format
    const dispatches = {
      items: [{
        TYPE: 7, // Standard dispatch type for sales
        NUMBER: requestData.irsaliye_no || '~',
        DATE: requestData.tarihi,
        TIME: convertTimeToLogoInt(requestData.saati),
        ARP_CODE: requestData.cari_kodu || '',
        SOURCEINDEX: requestData.ambar_kodu || 0,
        FACTORY: requestData.fabrika_kodu || 0,
        DIVISION: requestData.isyeri_kodu || 0,
        DEPARTMENT: requestData.bolum_kodu || 0,
        SHIP_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of sevk_tarihi
        SHIP_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of sevk_saati
        DELIVERY_DATE: requestData.irsaliye_tarihi || requestData.tarihi, // Use irsaliye_tarihi instead of teslim_tarihi
        DELIVERY_TIME: convertTimeToLogoInt(requestData.irsaliye_saati || requestData.saati), // Use irsaliye_saati instead of teslim_saati
        NOTES1: requestData.aciklama || '',
        PROJECT_CODE: requestData.proje_kodu || '',
        SALESMAN_CODE: requestData.satis_elemani || '',
        SALESMANREF: salesmanRef || 0,
        // REST API specific fields
        DISPATCH_SERIES: '', // irsaliye_seri not in schema
        DISPATCH_NUMBER_FORMAT: requestData.irsaliye_numarasi_formati || '',
      }],
    }

    return {
      INVOICE: invoice,
      TRANSACTIONS: transactions,
      DISPATCHES: dispatches,
      requestData, // Keep original request data for reference
    }
  },

  /**
   * Posts a sales invoice to the Logo REST API.
   * Assumes the endpoint is /SalesInvoices.
   */
  postSatisFatura: async ({
    accessToken,
    invoiceData,
    veritabaniId,
  }: {
    accessToken: string
    invoiceData: SatisFaturaInput
    veritabaniId: string
  }): Promise<{ INTERNAL_REFERENCE: number }> => { // Assuming response structure
    try {
      const logoConfig = await getLogoConfigById(veritabaniId)
      const restApiUrl = logoConfig.erp.rest_settings.rest_api_url

      if (!restApiUrl) {
        throw new Error(`Veritabanı ID için Logo REST API URL'si bulunamadı: ${veritabaniId}`)
      }

      const endpoint = `${restApiUrl}/SalesInvoices` // Varsayılan endpoint
      consola.info(`Satış faturası Logo'ya gönderiliyor: ${endpoint}`)
      // Format the data according to the expected structure
      const formattedData = {
        ...invoiceData.INVOICE,
        DISPATCHES: invoiceData.DISPATCHES,
        TRANSACTIONS: invoiceData.TRANSACTIONS,
      }
      const response = await fetch(endpoint, {
        method: 'POST',
        body: JSON.stringify(formattedData),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        },
      })

      // Try parsing JSON first, fallback to text if it fails
      let data: any
      try {
        data = await response.json()
      }
      catch {
        data = await response.text() // Get raw text if JSON parsing fails
      }

      if (!response.ok) {
        const errorDetail = typeof data === 'string' ? data : JSON.stringify(data)
        const errorMsg = `Logo Rest Servisi Hatası - postSatisFatura: Durum: ${response.status} ${response.statusText}. Yanıt: ${errorDetail}`
        consola.error(errorMsg, { requestBody: invoiceData })
        throw new Error(errorMsg)
      }

      // Assuming successful response contains INTERNAL_REFERENCE
      if (typeof data !== 'object' || data === null || typeof data.INTERNAL_REFERENCE !== 'number') {
        consola.warn('Logo Rest Servisi - postSatisFatura: Beklenmeyen yanıt formatı.', { responseData: data })
        // Attempt to provide a default or handle differently if necessary
        // For now, throw an error if INTERNAL_REFERENCE is missing/invalid
        throw new Error('Logo Rest Servisi - postSatisFatura: Yanıt formatı beklenmedik veya INTERNAL_REFERENCE eksik.')
      }

      consola.success(`Satış faturası Logo'ya başarıyla gönderildi. Referans No: ${data.INTERNAL_REFERENCE}`)
      return data as { INTERNAL_REFERENCE: number }
    }
    catch (error) {
      consola.error('Logo Rest Servisi Hatası - postSatisFatura:', error)
      throw error // Re-throw after logging
    }
  },

  /**
   * Posts a sales invoice to the Logo REST API with direct transformation from request data.
   * This function handles both transformation and API posting in one call.
   */
  postSatisFaturaFromRequest: async ({
    accessToken,
    requestData,
    veritabaniId,
  }: {
    accessToken: string
    requestData: z.infer<typeof satisFaturalariSchema>
    veritabaniId: string
  }): Promise<{ INTERNAL_REFERENCE: number }> => {
    // Transform request data to REST format
    const invoiceData = await SatisFaturalariLogoRestService.transformToRestFormat(requestData, veritabaniId)

    // Post to Logo REST API
    return await SatisFaturalariLogoRestService.postSatisFatura({
      accessToken,
      invoiceData,
      veritabaniId,
    })
  },
}

export default SatisFaturalariLogoRestService
