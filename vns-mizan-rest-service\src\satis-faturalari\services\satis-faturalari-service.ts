import type { z } from 'zod'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import { consola } from 'consola'
import LogoConfigService from '../../shared/services/logo-config-service.ts'
import SatisFaturaRestValidator from '../validators/rest-validator.ts'
import SatisFaturaSqlValidator from '../validators/sql-validator.ts'
import RestTransformationService from './rest-transformation-service.ts'
import SqlTransformationService from './sql-transformation-service.ts'

/**
 * Simplified Sales Invoice Service using the new transformer pattern
 * This replaces the complex transformation logic with clean separation
 */

/**
 * Fatura yanıt tipi
 */
interface InvoiceResponse {
  status: string
  data?: any
  error?: string
  logoRef?: number
  ficheNo?: string
  irsaliyeNo?: string
}

const SatisFaturalariService = {

  /**
   * Ana fatura işleme fonksiyonu
   */
  async handleCreateInvoice({
    veritabaniId,
    requestPayload,
    logoCredentials,
  }: {
    veritabaniId: string
    requestPayload: z.infer<typeof satisFaturalariSchema>
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<InvoiceResponse> {
    try {
      // Get Logo configuration to determine integration method
      const useRest = await LogoConfigService.getUseRestFlag(veritabaniId)

      if (useRest) {
        // Use REST API integration
        return await SatisFaturalariService.processWithRest({
          veritabaniId,
          requestPayload,
          logoCredentials,
        })
      }
      else {
        // Use direct SQL integration
        return await SatisFaturalariService.processWithSql({
          veritabaniId,
          requestPayload,
          logoCredentials,
        })
      }
    }
    catch (error: any) {
      consola.error('Fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'Bilinmeyen bir hata oluştu',
      }
    }
  },

  /**
   * REST API ile fatura işleme
   */
  async processWithRest({
    veritabaniId,
    requestPayload,
    logoCredentials,
  }: {
    veritabaniId: string
    requestPayload: z.infer<typeof satisFaturalariSchema>
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<InvoiceResponse> {
    try {
      // Validate request data for REST API
      const validationResult = await SatisFaturaRestValidator.validateSatisFaturaRequest({
        requestData: requestPayload,
        veritabaniId,
      })

      if (!validationResult.isValid) {
        return {
          status: 'error',
          error: validationResult.error || 'REST doğrulama başarısız oldu',
        }
      }

      // Process with REST transformation service
      const restResult = await RestTransformationService.processInvoiceWithRest({
        requestData: requestPayload,
        veritabaniId,
        logoCredentials,
      })

      if (!restResult.success) {
        return {
          status: 'error',
          error: restResult.error || 'REST API işlemi başarısız oldu',
        }
      }

      return {
        status: 'success',
        logoRef: restResult.logoRef,
        ficheNo: restResult.ficheNo,
        irsaliyeNo: restResult.irsaliyeNo,
        data: restResult.data,
      }
    }
    catch (error: any) {
      consola.error('REST API fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'REST API işlemi başarısız oldu',
      }
    }
  },

  /**
   * Direct SQL ile fatura işleme
   */
  async processWithSql({
    veritabaniId,
    requestPayload,
    logoCredentials,
  }: {
    veritabaniId: string
    requestPayload: z.infer<typeof satisFaturalariSchema>
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<InvoiceResponse> {
    try {
      // Validate request data for SQL integration
      const validationResult = await SatisFaturaSqlValidator.validateSatisFaturaRequest({
        requestData: requestPayload,
        veritabaniId,
      })

      if (!validationResult.isValid) {
        return {
          status: 'error',
          error: validationResult.error || 'SQL doğrulama başarısız oldu',
        }
      }

      // Process with SQL transformation service
      const sqlResult = await SqlTransformationService.processInvoiceWithSql({
        requestData: requestPayload,
        veritabaniId,
        logoCredentials,
      })

      if (!sqlResult.success) {
        return {
          status: 'error',
          error: sqlResult.error || 'SQL işlemi başarısız oldu',
        }
      }

      return {
        status: 'success',
        logoRef: sqlResult.logoRef,
        ficheNo: sqlResult.ficheNo,
        irsaliyeNo: sqlResult.irsaliyeNo,
      }
    }
    catch (error: any) {
      consola.error('SQL fatura işleme sırasında hata oluştu:', error)
      return {
        status: 'error',
        error: error.message || 'SQL işlemi başarısız oldu',
      }
    }
  },

}

export default SatisFaturalariService
