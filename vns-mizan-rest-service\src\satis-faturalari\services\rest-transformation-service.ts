import type { z } from 'zod'
import type { SatisFaturaInput } from '../models/sales-invoice.ts'
import type { satisFaturalariSchema } from '../routes/satis-faturalari-routes.ts'
import { consola } from 'consola'
import SatisFaturalariLogoRestService from './logo-rest-service.ts'
import DatabaseService from './database-service.ts'

/**
 * Service for handling REST API transformation and processing for Satis Faturalari
 * This service handles the complete REST flow: validation → transformation → API call → database save
 */
const RestTransformationService = {
  /**
   * Processes invoice with REST API integration
   * Handles transformation from request data to REST format, API call, and database save
   */
  async processInvoiceWithRest({
    requestData,
    veritabaniId,
    logoCredentials,
  }: {
    requestData: z.infer<typeof satisFaturalariSchema>
    veritabaniId: string
    logoCredentials?: { kullanici_adi: string, sifre: string }
  }): Promise<{
    success: boolean
    error?: string
    logoRef?: number
    ficheNo?: string
    irsaliyeNo?: string
    data?: any
  }> {
    try {
      // Transform request data to REST format
      const restData = await RestTransformationService.transformToRestFormat(requestData, veritabaniId)
      
      // Get access token
      const accessToken = await SatisFaturalariLogoRestService.getToken({
        veritabaniId,
        logoCredentials,
      })
      
      // Call Logo REST API
      const restResult = await SatisFaturalariLogoRestService.postSatisFatura({
        accessToken,
        invoiceData: restData,
        veritabaniId,
      })
      
      // TODO: Get fiche number and dispatch number from Logo database using INTERNAL_REFERENCE
      const ficheNo = 'TODO_GET_FROM_LOGO_DB'
      const irsaliyeNo = 'TODO_GET_FROM_LOGO_DB'
      
      // Save to application database
      await DatabaseService.insertSatisFatura({
        requestData,
        logoFaturaNo: ficheNo,
        logoIrsaliyeNo: irsaliyeNo,
        logoFaturaLogicalRef: restResult.INTERNAL_REFERENCE,
        veritabaniId,
        logoRestData: JSON.stringify(restData), // Log REST data
      })
      
      return {
        success: true,
        logoRef: restResult.INTERNAL_REFERENCE,
        ficheNo,
        irsaliyeNo,
        data: restResult,
      }
    }
    catch (error: any) {
      consola.error('REST API fatura işleme sırasında hata oluştu:', error)
      
      // Save error to database
      await DatabaseService.insertSatisFatura({
        requestData,
        errorMessage: error.message,
        veritabaniId,
        logoRestData: JSON.stringify({ error: error.message }), // Log error
      })
      
      return {
        success: false,
        error: error.message || 'REST API işlemi başarısız oldu',
      }
    }
  },

  /**
   * Transforms Mizan request data to REST API format for Logo REST API integration
   * Used when use_rest=true
   */
  async transformToRestFormat(
    requestData: z.infer<typeof satisFaturalariSchema>,
    veritabaniId: string,
  ): Promise<SatisFaturaInput> {
    // TODO: Add mapping logic here
    // This function should transform requestData to Logo REST API format
    // Include all necessary field mappings for INVOICE, TRANSACTIONS, and DISPATCHES
    
    consola.info('REST transformation başlatılıyor...')
    
    // Placeholder transformation - replace with actual mapping logic
    const invoice = {
      TYPE: requestData.fatura_turu,
      NUMBER: requestData.fatura_no || '~',
      DATE: requestData.tarihi,
      TIME: 0, // TODO: Convert requestData.saati to Logo time format
      // TODO: Add all other INVOICE fields mapping
    }
    
    const transactions = {
      items: (requestData.fatura_satirlari || []).map((satir, index) => ({
        TYPE: satir.satir_turu || 0,
        MASTER_CODE: satir.malzeme_kodu || '',
        QUANTITY: satir.miktar || 0,
        PRICE: satir.birim_fiyat || 0,
        // TODO: Add all other TRANSACTION fields mapping
      }))
    }
    
    const dispatches = {
      items: [{
        TYPE: 7, // Standard dispatch type for sales
        NUMBER: requestData.irsaliye_no || '~',
        DATE: requestData.tarihi,
        TIME: 0, // TODO: Convert requestData.saati to Logo time format
        // TODO: Add all other DISPATCH fields mapping
      }]
    }
    
    return {
      INVOICE: invoice,
      TRANSACTIONS: transactions,
      DISPATCHES: dispatches,
      requestData, // Keep original request data for reference
    }
  },
}

export default RestTransformationService
